<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class PermissionSeeder extends Seeder
{
    public array $data = [
        'UserController.paymentLogs',
        'BranchBoxController.boxCreate',
        'BranchBoxController.create',
        'BranchBoxController.createSection',
        'BranchBoxController.list',
        'BranchBoxController.listSection',
        'BranchBoxController.show',
        'BranchBoxController.showSection',
        'ContactController.index',
        'TaskController.index',
        'TaskController.create',
        'TaskController.update',
        'TaskController.delete',
        'GoodsController.statistics',
        'BranchBoxController.edit',
        'BranchBoxController.remove',
        'BranchBoxController.boxFill',
        'BranchBoxController.addContainer',
        'BranchBoxController.deleteContainer',
        'BranchBoxController.deliveryCreate',
        'BranchBoxController.deliveryList',
        'BranchBoxController.showDelivery',
        'BranchBoxController.editDelivery',
        'BranchBoxController.deleteDelivery',
        'BranchBoxController.deleteBoxContainer',
        'BranchBoxController.flightContainer',
        'BranchBoxController.flightDelivery',
        'BranchBoxController.boxes',
        'BranchBoxController.localDistributionForm',
        'BranchBoxController.localDistributionCreate',
        'BranchBoxController.localDistributionList',
        'BranchBoxController.localDistributionShow',
        'BranchBoxController.localDistributionEdit',
        'BranchBoxController.localDistributionRemove',
        'BranchBoxController.sectionQr',


        'HelperController.detectDuplicateGoodsInvoices',
        'ExcelController.export',
        'FlightsController.paginate',
        'FlightsController.index2',
        'FlightsController.store',
        'FlightsController.excel',
        'FlightsController.excel2',
        'FlightsController.create',
        'FlightsController.edit',
        'FlightsController.update',
        'FlightsController.delete',
        'FlightsController.delete2',
        'FlightsController.importScan',
        'FlightsController.canceledGoods',
        'GoodsController.deletedGoods',
        'GoodsController.tookOutGoods',
        'GoodsController.getBranchLogData',
        'GoodsController.branchLog',

        // good bulk actions
        'GoodsController.clearanceChange',
        'GoodsController.bulkUpdateStatus',
        //
        'GoodsController.recoverDeletedGood',
        'GoodsController.dashboard',
        'GoodsController.dashboard',
        'GoodsController.paginate',
        'GoodsController.paginate2',
        'GoodsController.paginate_courier',
        'GoodsController.store',
        'GoodsController.Goods_index',
        'GoodsController.index',
        'GoodsController.store',
        'GoodsController.create',
        'GoodsController.edit',
        'GoodsController.update',
        'GoodsController.delete',
        'GoodsController.findGoodById',
        'GoodsController.action',
        'GoodsController.delete2',
        'GoodsController.declaration',
        'GoodsController.declaration_update',
        'GoodsController.courier',
        'GoodsController.courier2',
        'GoodsController.getBranchLogData',
        'GoodsController.tookOutGoodsData',
        'GoodsController.deletedGoodsData',
        'CitiesController.index',
        'CitiesController.store',
        'CitiesController.create',
        'CitiesController.edit',
        'CitiesController.update',
        'CitiesController.delete',
        'CitiesController.delete2',
        'CitiesController.paginate',
        'ItemCategoryController.index',
        'ItemCategoryController.store',
        'ItemCategoryController.create',
        'ItemCategoryController.edit',
        'ItemCategoryController.update',
        'ItemCategoryController.delete',
        'ItemCategoryController.delete2',
        'ItemCategoryController.paginate',
        'ProhibitedItemsController.index',
        'ProhibitedItemsController.store',
        'ProhibitedItemsController.create',
        'ProhibitedItemsController.edit',
        'ProhibitedItemsController.update',
        'ProhibitedItemsController.delete',
        'ProhibitedItemsController.delete2',
        'ProhibitedItemsController.paginate',
        'BranchController.index',
        'BranchController.store',
        'BranchController.create',
        'BranchController.edit',
        'BranchController.update',
        'BranchController.delete',
        'BranchController.paginate',
        'BranchController.delete2',
        'ParcelCoordinateController.index',
        'ParcelCoordinateController.store',
        'ParcelCoordinateController.create',
        'ParcelCoordinateController.edit',
        'ParcelCoordinateController.update',
        'ParcelCoordinateController.paginate',
        'ParcelCoordinateController.delete',
        'ParcelCoordinateController.delete2',
        'CashflowController.index',
        'CashflowController.store',
        'CashflowController.create',
        'CashflowController.edit',
        'CashflowController.update',
        'CashflowController.delete',
        'CashflowController.delete2',
        'CashflowController.paginate',
        'CashflowController.paginate2',
        'CashflowController.invoice',
        'UserController.report',
        'UserController.index',
        'UserController.index2',
        'UserController.index3',
        'UserController.store',
        'UserController.create',
        'UserController.edit',
        'UserController.update',
        'UserController.delete',
        'UserController.delete2',
        'UserController.paginate',
        'UserController.paginate2',
        'UserController:balance_edit',
        'ParametersController.index',
        'ParametersController.price',
        'ParametersController.edit',
        'ParametersController.update',
        'TermsController.index',
        'TermsController.store',
        'TermsController.update',
        'TermsController.index2',
        'TermsController.store2',
        'TermsController.update2',
        'SMScontroller.index',
        'SMScontroller.all_users',
        'SMScontroller.sms_all_users_send',
        'SMScontroller.sms_all_users_with',
        'SMScontroller.update',
        'SMScontroller.db_all_users',
        'SMScontroller.db_all_users_send',
        'AboutController.index',
        'AboutController.store',
        'AboutController.update',
        'AboutController.index',
        'AboutController.store',
        'AboutController.update',
        'TransactionController.index',
        'TransactionController.index3',
        'TransactionController.paginate',
        'TransactionController.refund',
        'TransactionController.index2',
        'TransactionController.paginate2',
        'GoodsController.scanParcel',
        'RoomController.index',
        'RoomController.userSearch',
        'RoomController.orderTookOut',
        'RoomController.payment',
        'RoomController.paymentLog',
        'RoomController.paymentLogData',
        'DriverController',

        'PromotionController.index',
        'PromotionController.create',
        'PromotionController.edit',
        'PromotionController.remove',

        'PosterController.index',
        'PosterController.create',
        'PosterController.edit',
        'PosterController.remove',

        'PosterController.indexJob',
        'PosterController.createJob',
        'PosterController.editJob',
        'PosterController.removeJob',


    ];

    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        foreach ($this->data as $datum)
        {
            Permission::query()->updateOrCreate(['name' => $datum]);
        }
    }
}
