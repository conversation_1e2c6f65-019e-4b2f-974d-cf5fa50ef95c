<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SmsHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'message',
        'type',
        'flight_id',
        'branch_id',
        'status',
        'total_count',
        'processed_count',
        'last_processed_user_id',
    ];

    /**
     * Get the flight associated with the SMS history.
     */
    public function flight()
    {
        return $this->belongsTo(Flight::class);
    }

    /**
     * Get the branch associated with the SMS history.
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    /**
     * Get the recipients for the SMS history.
     */
    public function recipients()
    {
        return $this->hasMany(SmsRecipient::class);
    }
}
