<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SmsRecipient extends Model
{
    use HasFactory;

    protected $fillable = [
        'sms_history_id',
        'user_id',
        'phone',
        'status',
        'sent_at',
    ];

    protected $casts = [
        'sent_at' => 'datetime',
    ];

    /**
     * Get the SMS history that owns the recipient.
     */
    public function smsHistory(): BelongsTo
    {
        return $this->belongsTo(SmsHistory::class);
    }

    /**
     * Get the user that owns the recipient.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
