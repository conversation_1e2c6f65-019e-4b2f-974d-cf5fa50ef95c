<?php

namespace App\Http\Controllers;

use App\Jobs\SendBulkSmsJob;
use App\Models\Branch;
use App\Models\Flight;
use App\Models\GeneralNotification;
use App\Models\GeneralNotificationReaded;
use App\Models\goods;
use App\Models\Notification;
use App\Models\SmsHistory;
use App\Models\SmsRecipient;
use App\Models\SMSslider;
use App\Models\User;
use App\Notifications\db_news;
use App\Support\SMS;
use Illuminate\Http\Request;

class SMScontroller extends Controller
{
    //
    public function index()
    {
        $sms_slider = SMSslider::first();
        return view('admin.SMS.index', compact( 'sms_slider'));
    }
    public function all_users()
    {
        $users = User::all();
        $branches = Branch::all();
        $flights = Flight::with('parcelCoordinates')
            ->limit(100)
            ->orderByDesc('id')
            ->get();

        $smsHistories = SmsHistory::with(['flight', 'branch'])
            ->when(request('search'), function ($query) {
                $search = request('search');
                $query
                    ->whereHas('flight.goods.User', function ($query) use ($search){
                        $query->where('room_number', 'LIKE', "%{$search}%");
                    })
                ;
            })
            ->orderByDesc('id')
            ->paginate(10);

        return view('admin.SMS.all_users', compact('users', 'branches', 'flights', 'smsHistories'));
    }
    public function sms_all_users_send(Request $request)
    {
        $validated = $request->validate([
            'sms_text' => 'required',
        ]);
        $branch = $request->input('branch_id');
        $texty = $request->input('sms_text');
        $flightIds = $request->input('flight_ids');

        // Create SMS history record
        $smsHistory = SmsHistory::create([
            'message' => $texty,
            'type' => 'all_users',
            'branch_id' => $branch,
            'flight_id' => $flightIds ? $flightIds[0] : null, // Take first flight ID if multiple
            'status' => 'pending',
        ]);

        // Dispatch the job to send SMS in the background
        SendBulkSmsJob::dispatch($smsHistory);

        return redirect()->route("sms.all_users")->with("success", "SMS გაგზავნა დაიწყო. პროგრესის ნახვა შეგიძლიათ ისტორიის ცხრილში.");

    }

    public function sms_all_users_with(Request $request)
    {
        $validated = $request->validate([
            'sms_text2' => 'required',
        ]);

        $flight_id = $request->input('flight_id');
        $texty = $request->input('sms_text2');

        // Create SMS history record
        $smsHistory = SmsHistory::create([
            'message' => $texty,
            'type' => 'undeclared',
            'flight_id' => $flight_id,
            'status' => 'pending',
        ]);

        // Dispatch the job to send SMS in the background
        SendBulkSmsJob::dispatch($smsHistory);

        return redirect()->route("sms.all_users")->with("success", "SMS გაგზავნა დაიწყო. პროგრესის ნახვა შეგიძლიათ ისტორიის ცხრილში.");
    }

    public function update(SMSslider $sms,Request $request)
    {

//        $validated = $request->validate([
//            'description_en' => 'required',
//            'description_ru' => 'required',
//            'description_ge' => 'required',
//            'description_es' => 'required',
//            'description_uk' => 'required',
//            'title_en' => 'required',
//            'title_ru' => 'required',
//            'title_ge' => 'required',
//            'title_es' => 'required',
//            'title_uk' => 'required',
//            'delivered_to_tbilisi' => 'required',
//            'delivered_to_kutaisi' => 'required',
//            'delivered_to_batumi' => 'required',
//            'delivered_to_zugdidi' => 'required',
//            'delivered_to_rustavi' => 'required',
//            'delivered_to_dididighomi' => 'required',
//            'received_one_month' => 'nullable',
//            'received_two_day' => 'nullable',
//        ]);
        $sms->update($request->all());

        //ip address
//        $ipAddress = $request->ip();

        $sms->save();

        return redirect()->route("sms.index")->with("success", "sms წარმატებით დაემატა.");
    }



    public function db_all_users()
    {
        $notifications = GeneralNotification::query()->orderByDesc('id')->paginate(10);

        return view('admin.SMS.db_all_users', compact( 'notifications'));
    }
    //database notification
    public function db_all_users_send(Request $request)
    {
        $request->validate([
            'sms_text' => 'required'
        ]);
        $photoPath = null;
        if ($request->hasFile('photo')) {
            $photoPath = $request->file('photo')->store('notifications', 'public');
        }

        GeneralNotification::query()->create([
            'title' => $request->title,
            'text' => $request->sms_text,
            'image' => $photoPath
        ]);

        return back()->with('success', 'შეტყობინება წარმატებით დაემატა!');
//        return redirect()->route("sms.db_all_users")->with("success", "notification-ები წარმატებით გაიგზავნა");
    }

    /**
     * Get SMS recipients for a specific SMS history
     */
    public function getSmsRecipients(Request $request)
    {
        $smsHistoryId = $request->get('sms_history_id');
        $search = $request->get('search', '');

        $query = SmsRecipient::with(['user.branch'])
            ->where('sms_history_id', $smsHistoryId);

        if ($search) {
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('user_room_code', 'LIKE', "%{$search}%")
                  ->orWhere('first_name_ge', 'LIKE', "%{$search}%")
                  ->orWhere('last_name_ge', 'LIKE', "%{$search}%")
                  ->orWhereRaw("CONCAT(first_name_ge, ' ', last_name_ge) LIKE ?", ["%{$search}%"]);
            });
        }

        $recipients = $query->paginate(10);

        // Transform the data to include user information
        $recipients->getCollection()->transform(function ($recipient) {
            return [
                'id' => $recipient->id,
                'user_room_code' => $recipient->user->user_room_code ?? null,
                'first_name_ge' => $recipient->user->first_name_ge ?? null,
                'last_name_ge' => $recipient->user->last_name_ge ?? null,
                'branch' => $recipient->user->branch ? [
                    'title_ge' => $recipient->user->branch->title_ge
                ] : null,
                'phone' => $recipient->phone,
                'status' => $recipient->status,
                'sent_at' => $recipient->sent_at?->format('d.m.Y H:i'),
            ];
        });

        return response()->json($recipients);
    }

    public function loadMore()
    {
        $notIncludeIds = GeneralNotificationReaded::query()
            ->where('user_id', auth()->id())
            ->pluck('general_notification_id')
        ;

        $notification = GeneralNotification::query()
            ->whereNotIn('id', $notIncludeIds)
            ->first()
        ;

        if ($notification)
        {
            GeneralNotificationReaded::query()->create([
                'general_notification_id' => $notification->id,
                'user_id' => auth()->id(),
            ]);

            $notIncludeIds = GeneralNotificationReaded::query()
                ->where('user_id', auth()->id())
                ->pluck('general_notification_id')
            ;

            $notification = GeneralNotification::query()
                ->whereNotIn('id', $notIncludeIds)
                ->first()
            ;
        }

        return [
            'success' => (bool) $notification,
            'data' => $notification
        ];
    }


    public function markNotification(Request $request)
    {
//        dd($request);
        auth()->user()
            ->unreadNotifications
            ->when($request->input('id'), function ($query) use ($request) {
                return $query->where('id', $request->input('id'));
            })
            ->markAsRead();

        return response()->noContent();
    }

    public function destroy($id)
    {
        GeneralNotification::destroy($id);

        return back()->with('success', 'შეტყობინება წარმატებით წაიშალა!');
    }

    /**
     * Resume a failed SMS sending job.
     *
     * @param int $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function resumeSms($id)
    {
        $smsHistory = SmsHistory::findOrFail($id);

        // Only allow resuming failed jobs
        if ($smsHistory->status === 'failed') {
            $smsHistory->update(['status' => 'pending']);

            // Dispatch the job again
            SendBulkSmsJob::dispatch($smsHistory);

            return redirect()->route('sms.all_users')->with('success', 'SMS გაგზავნა განახლდა. პროგრესის ნახვა შეგიძლიათ ისტორიის ცხრილში.');
        }

        return redirect()->route('sms.all_users')->with('error', 'მხოლოდ შეჩერებული SMS გაგზავნის განახლებაა შესაძლებელი.');
    }
}
