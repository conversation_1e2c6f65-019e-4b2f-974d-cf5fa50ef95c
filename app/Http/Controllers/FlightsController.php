<?php

namespace App\Http\Controllers;

use App\Imports\FlightImport;
use App\Imports\GoodsImport;
use App\Imports\GoodsImport2;
use App\Imports\ScanGoodsCommand;
use App\Models\Branch;
use App\Models\CanceledParcel;
use App\Models\Flight;
use App\Models\FlightBranch;
use App\Models\goods;
use App\Models\ItemCategory;
use App\Models\ParcelCoordinates;
use App\Models\Promotion;
use App\Service\FlightService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class FlightsController extends Controller
{
    public function __construct(public FlightService $service)
    {
        //
    }

    public function importScan($id, Request $request)
    {
        $import = new ScanGoodsCommand($id);

        Excel::import($import, $request->file('file'));

        return [
            'missing_in_database' => $import->nakleboba,
            'redundant_in_database ' => $import->zedmetoba,
        ];
    }

    public function canceledGoods()
    {
        return CanceledParcel::query()->with('good')->get();
    }

    public function index()
    {
//        $flights = Flights::all();
//        return view('admin.Flights', ['flights' => $flights]);

        $flights = Flight::query()
            ->where('created_at', '>=', now()->subYear())
            ->orderBy("id", "desc")
            ->get()
            ->toArray();

        return view('admin.flights.index2', compact('flights'));
    }

    public function index2()
    {
//        $flights = Flights::all();
//        return view('admin.Flights', ['flights' => $flights]);

        $itemcategory = ItemCategory::all();
        $oneYearAgo = Carbon::now()->subYear();
        //$flights = Flight::where('created_at', '>=', $oneYearAgo)->orderBy("id", "desc")->get()->toArray();
//    dd($flights);
        $flights = Flight::where('id', 749)->first();
        return view('admin.flights.index2', compact('flights', 'itemcategory'));
    }

    public function create()
    {
        $parcel_coordinates = ParcelCoordinates::all();
        $promotions = Promotion::query()->where('active', true)->get();

        return view('admin.flights.create', compact("parcel_coordinates", 'promotions'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'zeddebuli_number' => '',
            'flight_real_number' => 'required',
            'nusxa_number' => '',
            'flight_parcel_state' => 'required',
            'cord_id' => 'required',
//            'send_date' => 'required',
//            'recieve_date' => 'required',
            'takeout_date' => 'required',
            'usd_sell_rate' => 'required',
            'cny_sell_rate' => 'required',
            'KG_PRICE_2' => 'required',
            'already_updated_remaining_in_sms_table' => 'required',
            'locked' => 'boolean',
        ]);

        $flight = Flight::create([
            "zeddebuli_number" => $request->input('zeddebuli_number'),
            "nusxa_number" => $request->input('nusxa_number'),
            "flight_real_number" => $request->input('flight_real_number'),
            "flight_number" => 1,
            "flight_parcel_state" => $request->input('flight_parcel_state'),
            "cord_id" => $request->input('cord_id'),
            "send_date" => $request->input('takeout_date'),
            "recieve_date" => $request->input('takeout_date'),
            "takeout_date" => $request->input('takeout_date'),
            "usd_sell_rate" => $request->input('usd_sell_rate'),
            "cny_sell_rate" => $request->input('cny_sell_rate'),
            "KG_PRICE_2" => $request->input('KG_PRICE_2'),
            "already_updated_remaining_in_sms_table" => $request->input('already_updated_remaining_in_sms_table'),
            'locked' => $request->input('locked') ?? false,
            'promotion_id' => $request->input('promotion_id') ?? null
        ]);

        //ip address
//        $ipAddress = $request->ip();
        $silver = 'SNH';
        $string = DB::table('flights')->latest('flight_number')->first();
        //explode the string to get the number part, last is a laravel helper
        $number = last(explode("-", $string->flight_number));
        //increment the number by 1 and pad with 0 in left.

        $new = str_pad(intval($number) + 1, 5, 0, STR_PAD_LEFT);
        $flight->flight_number = $silver . '-' . $new;
        $flight->save();

        // ლოგირება
        \App\Models\AdminActivityLog::createLog(
            'create',
            "ფრენის შექმნა: {$flight->flight_number}",
            'Flight',
            $flight->id,
            null,
            $request->only(['flight_real_number', 'flight_parcel_state', 'cord_id', 'takeout_date', 'usd_sell_rate', 'cny_sell_rate', 'KG_PRICE_2'])
        );

        return redirect()->route("flights.index2")->with("success", "ფრენა წარმატებით დაემატა.");
    }

    public function edit(Flight $flight)
    {
//        dd(123);

        $branches = Branch::all();
        $deliveredBranches = FlightBranch::query()->with('user')->where('flight_id', $flight->id)->get();
//        dd($deliveredBranches);
        $parcel_coordinates = ParcelCoordinates::all();
        $flight->load('flightBranches');
        $promotions = Promotion::query()->where('active', true)->get();

        return view("admin.flights.edit", compact("flight", "parcel_coordinates", "branches", "deliveredBranches", 'promotions'));
    }

//    savaraudod aq unda chaematos statusi archevis shemdeg mosaxdeli velebis daangarishebebi
    public function update(Flight $flight, Request $request)
    {
        // ძველი მნიშვნელობების შენახვა ლოგირებისთვის
        $oldValues = $flight->only([
            'flight_number', 'flight_real_number', 'zeddebuli_number', 'nusxa_number',
            'flight_parcel_state', 'cord_id', 'send_date', 'recieve_date', 'takeout_date',
            'usd_sell_rate', 'cny_sell_rate', 'KG_PRICE_2', 'locked', 'declaration_lock'
        ]);

        $old_flight_parcel_state = $flight->flight_parcel_state;
        $validated = $request->validate([
            'zeddebuli_number' => '',
            'flight_number' => 'required',
            'flight_real_number' => 'required',
            'nusxa_number' => '',
            'flight_parcel_state' => 'required',
            'cord_id' => 'required',
//            'send_date' => 'required',
            'takeout_date' => 'required',
//            'takeout_date' => 'required',
            'usd_sell_rate' => 'required',
            'cny_sell_rate' => 'required',
            'KG_PRICE_2' => 'required',
            'already_updated_remaining_in_sms_table' => 'required',
            'locked' => 'boolean',
            'declaration_lock' => 'boolean'
        ]);
        $oldFlightStatus = $flight->flight_parcel_state;
        $oldFlightLocked = $flight->locked;
        $flight->update([
            "flight_number" => $request->input('flight_number'),
            "flight_real_number" => $request->input('flight_real_number'),
            "zeddebuli_number" => $request->input('zeddebuli_number'),
            "nusxa_number" => $request->input('nusxa_number'),
            "flight_parcel_state" => $request->input('flight_parcel_state'),
            "cord_id" => $request->input('cord_id'),
            "send_date" => $request->input('takeout_date'),
            "recieve_date" => $request->input('takeout_date'),
            "takeout_date" => $request->input('takeout_date'),
            "usd_sell_rate" => $request->input('usd_sell_rate'),
            "cny_sell_rate" => $request->input('cny_sell_rate'),
            "KG_PRICE_2" => $request->input('KG_PRICE_2'),
            "already_updated_remaining_in_sms_table" => $request->input('already_updated_remaining_in_sms_table'),
            'locked' => $request->input('locked') ?? false,
            'declaration_lock' => $request->input('declaration_lock') ?? false,
            'promotion_id' => $request->input('promotion_id') ?? null
        ]);

        if ($oldFlightLocked == 1 and
            $request->input('locked') == 0 and
            $flight->flight_parcel_state == "DELIVERED_OK")
        {
            $canceledParcels = CanceledParcel::query()->where('flight_id', $flight->id)->get();
            goods::query()->whereIn('id', $canceledParcels->pluck('good_id'))->update(['flight_parcel_state' => 'RECIEVED']);
            CanceledParcel::where('flight_id', $flight->id)->delete();
        }

        $sms_status = $request->input('already_updated_remaining_in_sms_table');
//        unikaluri amanatebis shesanaxi masivi rom mxolod magat gaegzavnot sms

        $this->service->applyStatus(
            $flight,
            $request->input('branches', []),
            $sms_status,
            $oldFlightStatus
        );

//        add must customize to users whos total client buy amount is greater than 300
//        if($flight->flight_parcel_state=="SENT" and $old_flight_parcel_state!="SENT"){

        $sum_client_buy_amount = goods::selectRaw('user_id, sum(client_buy_amount) as total_goods_value')
            ->where('flight_id', $flight->id)
            ->groupBy('user_id')
            ->get();

        //where user has 2 parcels and both of them are above 300
        foreach ($sum_client_buy_amount as $user_sum) {
            if ($user_sum->total_goods_value >= 300) {

                $change_goods2 = goods::where("flight_id", $flight->id)
                    ->where("user_id", $user_sum->user_id)
                    ->get();
                foreach ($change_goods2 as $change_goods3) {
                    $change_goods3->must_customize = 1;
                    $change_goods3->save();
                }

            }
        }

        // ლოგირება
        $newValues = $flight->only([
            'flight_number', 'flight_real_number', 'zeddebuli_number', 'nusxa_number',
            'flight_parcel_state', 'cord_id', 'send_date', 'recieve_date', 'takeout_date',
            'usd_sell_rate', 'cny_sell_rate', 'KG_PRICE_2', 'locked', 'declaration_lock'
        ]);

        \App\Models\AdminActivityLog::createLog(
            'update',
            "ფრენის განახლება: {$flight->flight_number}",
            'Flight',
            $flight->id,
            $oldValues,
            $newValues
        );

        return redirect()->route("flights.index2", $flight->id)->with("success", "ფრენა წარმატებით დარედაქტირდა.");
    }

    public function delete(Flight $flight)
    {
        // ლოგირება წაშლამდე
        \App\Models\AdminActivityLog::createLog(
            'delete',
            "ფრენის წაშლა: {$flight->flight_number}",
            'Flight',
            $flight->id,
            $flight->only(['flight_number', 'flight_real_number', 'flight_parcel_state', 'cord_id']),
            null
        );

        $flight->delete();
        return redirect()->route("flights.index2", $flight->id)->with("success", "ფრენა წარმატებით წაიშალა.");
    }

    public function delete2(Request $request)
    {
        //
        $id = $request->input('flight_id2');
        $flight = Flight::find($id);

        // ლოგირება წაშლამდე
        \App\Models\AdminActivityLog::createLog(
            'delete',
            "ფრენის წაშლა: {$flight->flight_number}",
            'Flight',
            $flight->id,
            $flight->only(['flight_number', 'flight_real_number', 'flight_parcel_state', 'cord_id']),
            null
        );

        $flight->delete();
        return redirect()->route("flights.index2", $flight->id)->with("success", "ფრენა წარმატებით წაიშალა.");
    }

    public function excel(Request $request)
    {
        $validated = $request->validate([
            'excel' => 'required|file|mimes:xlsx,xls',
            'flight_id' => 'integer|required',
        ]);

//        $import = new GoodsImport($validated["flight_id"]);

        $import = new FlightImport($validated["flight_id"]);
        Excel::import($import, $request->file('excel'));

        if(count($import->fail)>0)
        {
            return redirect()->back()->with('excel_error', $import->fail);
        }
        return redirect()->back()->with('success', 'წარმატებით დაიმპორტდა');
    }

    public function excel2(Request $request)
    {
        $validated = $request->validate([
            'excel1' => 'required',
            'flight_id1' => 'integer|required',
        ]);

        try {
            if ($request->hasFile('excel')) {
                $file = $request->file('excel1');
                $import = new GoodsImport2($validated["flight_id1"]);
                Excel::import($import, $file);
                $message = $import->getMessage();
                $message_type = $import->getMessageType();
            }
        } catch (Exception $e) {
            $message = "დაიმპორტების დროს წარმოიქმნა პრობლემა";
            $message_type = "error";
        }

        if ($message_type == "excel_error") {
            return redirect()->back()->with("excel_error", $message);
        } else {
            return redirect()->back()->with($message_type, $message);
        }
    }

    //admin flights paginate
    public function paginate(Request $request)
    {
        $perPage = $request->input("pagination")['perpage'];
        $currentPage = $request->input("pagination")['page'];
        $search = $request->input("query")['generalSearch'] ?? null;
        $status = $request->input("query")['status'] ?? null;
        $country = $request->input("query")['country'] ?? null;

        $query = Flight::query();

        if ($search) {
            $query->where(function ($innerQuery) use ($search) {
                $innerQuery->where("flight_number", "like", "%" . $search . "%")
                    ->orWhere("flight_real_number", "like", "%" . $search . "%")
                    ->orWhere("zeddebuli_number", "like", "%" . $search . "%")
                    ->orWhere("nusxa_number", "like", "%" . $search . "%");
            });
        }

        if ($status) {
            $query->where("flight_parcel_state", $status);
        }

        if ($country) {
            $query->where("cord_id", $country);
        }

        $total = $query->count();

        $flights = $query->orderBy('id', 'desc')
            ->skip(($currentPage - 1) * $perPage)
            ->take($perPage)
            ->get();

        $response = [
            'current_page' => $currentPage,
            'data' => $flights,
            'first_page_url' => $request->url() . '?page=1',
            'from' => ($currentPage - 1) * $perPage + 1,
            'last_page' => ceil($total / $perPage),
            'last_page_url' => $request->url() . '?page=' . ceil($total / $perPage),
            'links' => [
                [
                    'url' => null,
                    'label' => '&laquo; წინა',
                    'active' => false,
                ],
                // Add other links as needed
            ],
            'meta' => [
                'page' => $currentPage,
                'pages' => ceil($total / $perPage),
                'perpage' => $perPage,
                'total' => $total,
                'sort' => 'desc',
                'field' => 'flight_number',
            ],
            'next_page_url' => $currentPage < ceil($total / $perPage) ? $request->url() . '?page=' . ($currentPage + 1) : null,
            'path' => $request->url(),
            'per_page' => $perPage,
            'prev_page_url' => $currentPage > 1 ? $request->url() . '?page=' . ($currentPage - 1) : null,
            'to' => ($currentPage - 1) * $perPage + count($flights),
            'total' => $total,
        ];

        return response()->json($response);
    }
    //kamazidan gadmotanili modal

    public function flightSearch(Request $request): \Illuminate\Database\Eloquent\Collection|array
    {
        $request->validate([
            'search' => 'required'
        ]);

        return Flight::query()
            ->where('flight_number', 'like', "%$request->search%")
            ->select(['id', 'flight_number'])
            ->get()
        ;
    }

}
