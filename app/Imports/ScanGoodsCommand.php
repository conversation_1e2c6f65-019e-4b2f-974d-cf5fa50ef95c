<?php

namespace App\Imports;

use App\Models\goods;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;

class ScanGoodsCommand implements ToCollection
{
    public $zedmetoba;
    public $nakleboba;

    public function __construct(public $flightId) {}


    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        $importedCodes = $collection->pluck(1)->toArray();

//        goods::query()
//            ->select(['id', 'flight_id', 'tracking_code'])
//            ->where('flight_id', $this->flightId)
//            ->whereIn('tracking_code', $importedCodes)
//        ;

        $existingCodes = Goods::query()
            ->where('flight_id', $this->flightId)
            ->pluck('tracking_code')
            ->toArray()
        ;

        $this->nakleboba = array_diff($importedCodes, $existingCodes);

        $this->zedmetoba = array_diff($existingCodes, $importedCodes);
    }
}
