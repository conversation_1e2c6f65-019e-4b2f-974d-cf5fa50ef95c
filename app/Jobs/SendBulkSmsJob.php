<?php

namespace App\Jobs;

use App\Models\Branch;
use App\Models\Flight;
use App\Models\SmsHistory;
use App\Models\SmsRecipient;
use App\Models\User;
use App\Support\SMS;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendBulkSmsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of users to process per batch.
     *
     * @var int
     */
    protected $batchSize = 50;

    /**
     * The SMS history record.
     *
     * @var SmsHistory
     */
    protected $smsHistory;

    /**
     * Create a new job instance.
     *
     * @param SmsHistory $smsHistory
     * @return void
     */
    public function __construct(SmsHistory $smsHistory)
    {
        $this->smsHistory = $smsHistory;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Update status to in_progress
        $this->smsHistory->update(['status' => 'in_progress']);

        try {
            // Get the query based on the SMS type
            $query = $this->getUsersQuery();

            // If we have a last processed user ID, start from there
            if ($this->smsHistory->last_processed_user_id) {
                $query->where('id', '>', $this->smsHistory->last_processed_user_id);
            }

            // Get total count if not already set
            if ($this->smsHistory->total_count == 0) {
                $this->smsHistory->update([
                    'total_count' => $this->getUsersQuery()->count()
                ]);
            }

            // Process users in batches
            $query->orderBy('id')->chunk($this->batchSize, function ($users) {
                foreach ($users as $user) {
                    $status = 'sent';
                    $sentAt = now();

                    try {
                        // Send SMS
                        (new SMS($user->phone, $this->smsHistory->message))->send();
                    } catch (\Exception $e) {
                        $status = 'failed';
                        Log::error('SMS sending failed for user ' . $user->id . ': ' . $e->getMessage());
                    }

                    // Save recipient record
                    SmsRecipient::create([
                        'sms_history_id' => $this->smsHistory->id,
                        'user_id' => $user->id,
                        'phone' => $user->phone,
                        'status' => $status,
                        'sent_at' => $sentAt,
                    ]);

                    // Update the SMS history with progress
                    $this->smsHistory->update([
                        'processed_count' => $this->smsHistory->processed_count + 1,
                        'last_processed_user_id' => $user->id
                    ]);
                }
            });

            // Update status to completed
            $this->smsHistory->update(['status' => 'completed']);
        } catch (\Exception $e) {
            // Log the error
            Log::error('SMS sending failed: ' . $e->getMessage());

            // Update status to failed
            $this->smsHistory->update(['status' => 'failed']);
        }
    }

    /**
     * Get the query to retrieve users based on SMS type.
     *
     * @return \Illuminate\Database\Eloquent\Builder
     */
    protected function getUsersQuery()
    {
        $query = User::query()->where('user_wants_sms', 1);

        switch ($this->smsHistory->type) {
            case 'all_users':
                // If branch is specified, filter by branch
                if ($this->smsHistory->branch_id) {
                    $query->where('branch_id', $this->smsHistory->branch_id);
                }
                break;

            case 'undeclared':
                $query->whereHas('goods', function ($query) {
                    $query->where('is_declared', '0');
                    
                    // If flight is specified, filter by flight
                    if ($this->smsHistory->flight_id) {
                        $query->where('flight_id', $this->smsHistory->flight_id);
                    }
                });
                break;
        }

        return $query->select('id', 'phone', 'user_room_code', 'first_name_ge', 'last_name_ge', 'branch_id')->orderBy('id');
    }
}
