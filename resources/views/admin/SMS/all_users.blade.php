@extends('layouts.admin')
@section('content')
    <div class="d-flex flex-column-fluid">
        <div class=" container ">
            <div class="card card-custom">
                <div class="card-header flex-wrap border-0 pt-6 pb-0">
                    <div class="card-title">
                        <h3 class="card-label geo">SMS ყველა მომხმარებელთან!
                            <div class="text-muted pt-2 font-size-sm geo">მომხმარებელთან გასაგზავნი ტექსტი</div>
                        </h3>
                    </div>

                </div>
                <form class="form" id="kt_form" action="{{route("sms.sms_all_users_send")}}" method="POST">
                    @csrf
                    <div class="card-body">
                        <div class="col my-2 my-md-0">
                            <div class="form-group">
                                <label class="">ფრენები/ფილიალი:</label>
                                <select class="form-control " name="branch_id">
                                    <option value="">ყველა</option>
                                    @foreach($branches as $branch)
                                        <option value="{{$branch->id}}">{{$branch->title_en}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

                        <div class="col my-2 my-md-0">
                            <div class="form-group">
                                <label for="flight_status">რეისი</label>
                                <select class="form-control select2" name="flight_ids[]" multiple="multiple">
                                    <option value="" disabled>ყველა</option>
                                    @foreach($flights as $flight)
                                        <option value="{{$flight->id}}" {{ in_array($flight->id, old('flight_ids', [])) ? 'selected' : '' }}>
                                            {{$flight->flight_real_number}}-{{$flight->parcelCoordinates?->tab_name}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>


                        <div class="form-group mb-1">
                            <label for="exampleTextarea" class="geo">ყურადღება ! მოკლე ტექსტური შეტყობინება ყველა მომხმარებელთან გაიგზავნება.</label>
                            <textarea name="sms_text" class="form-control"
                                      id="exampleTextarea" rows="3"></textarea>
                        </div>
                        <button type="submit" class="geo btn btn-primary font-weight-bold geo">
                            <i class="ki ki-check icon-sm"></i>
                            გაგზავნა
                        </button>

                    </div>

                </form>

                <div class="card-header flex-wrap border-0 pt-6 pb-0">
                    <div class="card-title">
                        <h3 class="card-label geo">SMS ყველა მომხმარებელთან ! ვისაც არ აქვს დეკლარაცია შევსებული
                            <div class="text-muted pt-2 font-size-sm geo">მომხმარებელთან გასაგზავნი ტექსტი</div>
                        </h3>
                    </div>

                </div>
{{--                SMS daudeklarirebel amanatebze--}}
                <form class="form" id="kt_form2" action="{{route("sms.sms_all_users_with")}}" method="POST">
                    @csrf
                    <div class="card-body">
{{--                        <div class="col my-2 my-md-0">--}}
{{--                            <div class="form-group">--}}
{{--                                <label class="">მომხმარებლები:</label>--}}
{{--                                <select class="form-control " name="user_group">--}}
{{--                                    <option value="ALL">ყველა</option>--}}
{{--                                    <option value="SNXT">SNXT</option>--}}
{{--                                    <option value="SNXK">SNXK</option>--}}
{{--                                    <option value="SNX">SNX</option>--}}
{{--                                </select>--}}

{{--                            </div>--}}
{{--                        </div>--}}
                        <div class="col my-2 my-md-0">
                            <div class="form-group">
                                <label class="">ფრენა:</label>
                                <select class="form-control " name="flight_id">
                                    <option value="">ყველა</option>
                                    @foreach($flights as $flight)
                                        <option value="{{$flight->id}}">{{$flight->flight_number}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>

{{--                        <div class="col my-2 my-md-0">--}}
{{--                            <div class="form-group">--}}
{{--                                <label class="">დეკლარაცია:</label>--}}
{{--                                <select class="form-control " name="declaration_status">--}}
{{--                                    <option value="1">არ აქვს შევსებული</option>--}}
{{--                                    <option value="2">ყველა</option>--}}
{{--                                </select>--}}
{{--                            </div>--}}
{{--                        </div>--}}

                        <div class="form-group mb-1">
                            <label for="exampleTextarea" class="geo">ყურადღება ! მოკლე ტექსტური შეტყობინება ყველა მომხმარებელთან გაიგზავნება.</label>
                            <textarea name="sms_text2" class="form-control"
                                      id="exampleTextarea2" rows="3"></textarea>
                        </div>
                        <button type="submit" class="geo btn btn-primary font-weight-bold geo">
                            <i class="ki ki-check icon-sm"></i>
                            გაგზავნა
                        </button>

                    </div>

                </form>

                <!-- SMS History Section -->
                <div class="card-header flex-wrap border-0 pt-6 pb-0 mt-5">
                    <div class="card-title">
                        <h3 class="card-label geo">SMS გაგზავნის ისტორია
                            <div class="text-muted pt-2 font-size-sm geo">გაგზავნილი SMS-ების ისტორია</div>
                        </h3>
                    </div>
                </div>

                <div class="card-body">
                    <div class="mb-3">
                        <input type="text" id="sms-history-search" class="form-control" placeholder="ძიება შეტყობინებით, ტიპით, ფრენით..." value="{{ request('search') }}">
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>შეტყობინება</th>
                                    <th>ტიპი</th>
                                    <th>ფრენა</th>
                                    <th>ფილიალი</th>
                                    <th>სტატუსი</th>
                                    <th>რაოდენობა</th>
                                    <th>თარიღი</th>
                                    <th>მოქმედება</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($smsHistories as $history)
                                <tr>
                                    <td>{{ $history->id }}</td>
                                    <td>{{ $history->message }}</td>
                                    <td>
                                        @if($history->type == 'all_users')
                                            ყველა მომხმარებელი
                                        @elseif($history->type == 'undeclared')
                                            დაუდეკლარირებელი
                                        @else
                                            {{ $history->type }}
                                        @endif
                                    </td>
                                    <td>{{ $history->flight ? $history->flight->flight_number : '-' }}</td>
                                    <td>{{ $history->branch ? $history->branch->title_ge : '-' }}</td>
                                    <td>
                                        @if($history->status == 'pending')
                                            <span class="badge badge-warning">მიმდინარეობს</span>
                                        @elseif($history->status == 'in_progress')
                                            <span class="badge badge-info">პროცესშია</span>
                                        @elseif($history->status == 'completed')
                                            <span class="badge badge-success">დასრულებული</span>
                                        @elseif($history->status == 'failed')
                                            <span class="badge badge-danger">შეჩერებული</span>
                                        @endif
                                    </td>
                                    <td>{{ $history->processed_count }}/{{ $history->total_count }}</td>
                                    <td>{{ $history->created_at->format('d.m.Y H:i') }}</td>
                                    <td>
                                        @if($history->status == 'failed')
                                            <a href="{{ route('sms.resume', $history->id) }}" class="btn btn-sm btn-primary">გაგრძელება</a>
                                        @endif
                                        @if($history->status == 'completed')
                                            <button type="button" class="btn btn-sm btn-info" onclick="showSmsRecipients({{ $history->id }})">
                                                მომხმარებლები
                                            </button>
                                        @endif
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $smsHistories->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- SMS Recipients Modal -->
    <div class="modal fade" id="smsRecipientsModal" tabindex="-1" role="dialog" aria-labelledby="smsRecipientsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title geo" id="smsRecipientsModalLabel">SMS მიმღები მომხმარებლები</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Search Input -->
                    <div class="mb-3">
                        <input type="text" id="recipientSearch" class="form-control" placeholder="ძიება ოთახის ნომრით, სახელით, გვარით...">
                    </div>

                    <!-- Recipients Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="recipientsTable">
                            <thead>
                                <tr>
                                    <th class="geo">ოთახის ნომერი</th>
                                    <th class="geo">სახელი გვარი</th>
                                    <th class="geo">ფილიალი</th>
                                </tr>
                            </thead>
                            <tbody id="recipientsTableBody">
                                <!-- Recipients will be loaded here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-3" id="recipientsPagination">
                        <!-- Pagination will be loaded here -->
                    </div>

                    <!-- Loading indicator -->
                    <div class="text-center" id="recipientsLoading" style="display: none;">
                        <div class="spinner-border" role="status">
                            <span class="sr-only">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">დახურვა</button>
                </div>
            </div>
        </div>
    </div>

@endsection


@section("scripts")
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    <script>
        $(document).ready(function () {
            let searchTimeout;

            $('#sms-history-search').on('keyup', function () {
                clearTimeout(searchTimeout);
                let query = $(this).val();

                searchTimeout = setTimeout(function () {
                    let url = new URL(window.location.href);
                    if (query.length > 0) {
                        url.searchParams.set('search', query);
                    } else {
                        url.searchParams.delete('search');
                    }
                    window.history.replaceState(null, '', url.toString());

                    $.get(url.toString(), function (response) {
                        let html = $(response).find('table tbody').html();
                        $('table tbody').html(html);

                        let pagination = $(response).find('.d-flex.justify-content-center').html();
                        $('.d-flex.justify-content-center').html(pagination);
                    });

                }, 600);
            });



            $('.select2').select2();
        });

        // SMS Recipients Modal Functions
        let currentSmsHistoryId = null;
        let recipientSearchTimeout = null;

        function showSmsRecipients(smsHistoryId) {
            currentSmsHistoryId = smsHistoryId;
            $('#smsRecipientsModal').modal('show');
            loadRecipients(1, ''); // Load first page with no search
        }

        function loadRecipients(page = 1, search = '') {
            $('#recipientsLoading').show();
            $('#recipientsTableBody').empty();
            $('#recipientsPagination').empty();

            $.ajax({
                url: '{{ route("sms.recipients") }}',
                method: 'GET',
                data: {
                    sms_history_id: currentSmsHistoryId,
                    page: page,
                    search: search
                },
                success: function(response) {
                    $('#recipientsLoading').hide();

                    // Populate table
                    let tbody = '';
                    response.data.forEach(function(recipient) {
                        tbody += `
                            <tr>
                                <td>${recipient.user_room_code || '-'}</td>
                                <td>${(recipient.first_name_ge || '') + ' ' + (recipient.last_name_ge || '')}</td>
                                <td>${recipient.branch ? recipient.branch.title_ge : '-'}</td>
                            </tr>
                        `;
                    });
                    $('#recipientsTableBody').html(tbody);

                    // Populate pagination
                    if (response.last_page > 1) {
                        let pagination = '<nav><ul class="pagination justify-content-center">';

                        // Previous button
                        if (response.current_page > 1) {
                            pagination += `<li class="page-item"><a class="page-link" href="#" onclick="loadRecipients(${response.current_page - 1}, '${search}')">წინა</a></li>`;
                        }

                        // Page numbers
                        for (let i = Math.max(1, response.current_page - 2); i <= Math.min(response.last_page, response.current_page + 2); i++) {
                            let activeClass = i === response.current_page ? 'active' : '';
                            pagination += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="loadRecipients(${i}, '${search}')">${i}</a></li>`;
                        }

                        // Next button
                        if (response.current_page < response.last_page) {
                            pagination += `<li class="page-item"><a class="page-link" href="#" onclick="loadRecipients(${response.current_page + 1}, '${search}')">შემდეგი</a></li>`;
                        }

                        pagination += '</ul></nav>';
                        $('#recipientsPagination').html(pagination);
                    }
                },
                error: function() {
                    $('#recipientsLoading').hide();
                    alert('შეცდომა მონაცემების ჩატვირთვისას');
                }
            });
        }

        // Search functionality
        $('#recipientSearch').on('keyup', function() {
            clearTimeout(recipientSearchTimeout);
            let search = $(this).val();

            recipientSearchTimeout = setTimeout(function() {
                loadRecipients(1, search);
            }, 500);
        });
    </script>

    <script>

        // Class definition

        var KTSummernoteDemo = function () {
            // Private functions
            var demos = function () {
                $('.summernote').summernote({
                    height: 250

                });
            }

            return {
                // public functions
                init: function() {
                    demos();
                }
            };
        }();

        // Initialization
        jQuery(document).ready(function() {
            KTSummernoteDemo.init();
        });
    </script>
@endsection




